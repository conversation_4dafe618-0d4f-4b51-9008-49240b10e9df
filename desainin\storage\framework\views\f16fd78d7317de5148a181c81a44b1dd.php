<!DOCTYPE html>
<html lang="id" dir="ltr">

<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">

<!-- Document Title -->
<title>Desain.In | Jasa Desain Grafis Profesional</title>

<!-- Favicons -->
<link rel="apple-touch-icon" sizes="180x180" href="assets/img/favicons/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="assets/img/favicons/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="assets/img/favicons/favicon-16x16.png">
<link rel="shortcut icon" type="image/x-icon" href="assets/img/favicons/favicon.png">
<link rel="manifest" href="assets/img/favicons/manifest.json">
<meta name="msapplication-TileImage" content="assets/img/favicons/mstile-150x150.png">
<meta name="theme-color" content="#ffffff">

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

<style>
    :root {
        --primary-blue: #235FF5;
        --secondary-blue: #5B9BD5;
        --light-blue: #E3F2FD;
        --orange: #FFC436;
        --white: #FFFFFF;
        --dark-text: #2C3E50;
        --gray-text: #7B8794;
        --light-gray: #F5F7FA;
        --border-color: #E1E8ED;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Poppins', sans-serif;
        line-height: 1.6;
        color: var(--dark-text);
        background: var(--white);
    }

    /* Header dengan background orange seperti gambar */
    .top-bar {
        background: linear-gradient(90deg, var(--orange) 0%, #FFB74D 100%);
        height: 8px;
    }

    .navbar {
        background: var(--white);
        box-shadow: 0 2px 4px rgba(0,0,0,0.08);
        padding: 1rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .navbar-brand {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-blue) !important;
        text-decoration: none;
    }

    .nav-link {
        color: var(--dark-text) !important;
        font-weight: 500;
        margin: 0 1.5rem;
        font-size: 0.95rem;
    }

    .nav-link:hover {
        color: var(--primary-blue) !important;
    }

    .btn-mulai {
        background: var(--primary-blue);
        border: none;
        color: white;
        padding: 0.6rem 1.8rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-mulai:hover {
        background: var(--secondary-blue);
        color: white;
        transform: translateY(-1px);
    }

    /* Hero section dengan background putih dan ilustrasi */
    .hero-section {
        background: var(--white);
        padding: 6rem 0 4rem;
        position: relative;
    }

    .hero-content h1 {
        font-size: 3.2rem;
        font-weight: 700;
        color: var(--dark-text);
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero-content p {
        font-size: 1.1rem;
        color: var(--gray-text);
        margin-bottom: 2.5rem;
        line-height: 1.6;
    }

    .btn-portfolio {
        background: var(--primary-blue);
        border: none;
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
        transition: all 0.3s ease;
    }

    .btn-portfolio:hover {
        background: var(--secondary-blue);
        color: white;
        transform: translateY(-2px);
    }

    .btn-video {
        background: transparent;
        border: 2px solid var(--primary-blue);
        color: var(--primary-blue);
        padding: 0.8rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-video:hover {
        background: var(--primary-blue);
        color: white;
    }

    /* Features section dengan background abu-abu muda */
    .features-section {
        padding: 5rem 0;
        background: var(--light-gray);
    }

    .section-title {
        font-size: 2.8rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 4rem;
        color: var(--dark-text);
    }

    .feature-card {
        background: var(--white);
        padding: 2.5rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        text-align: center;
        margin-bottom: 2rem;
        transition: transform 0.3s ease;
        border: 1px solid var(--border-color);
    }

    .feature-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        margin: 0 auto 1.5rem;
        background: var(--light-blue);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-blue);
        font-size: 1.8rem;
    }

    .feature-card h4 {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--dark-text);
    }

    .feature-card p {
        color: var(--gray-text);
        line-height: 1.6;
        font-size: 0.95rem;
    }

    /* Why section */
    .why-section {
        padding: 5rem 0;
        background: var(--white);
    }

    .check-item {
        display: flex;
        align-items: start;
        margin-bottom: 2rem;
    }

    .check-icon {
        width: 24px;
        height: 24px;
        background: var(--primary-blue);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.8rem;
        margin-right: 1rem;
        margin-top: 0.2rem;
        flex-shrink: 0;
    }

    /* Help section dengan background biru muda */
    .help-section {
        padding: 4rem 0;
        background: var(--light-blue);
        text-align: center;
    }

    .help-section h2 {
        color: var(--dark-text);
        margin-bottom: 1.5rem;
    }

    .btn-konsultasi {
        background: var(--orange);
        border: none;
        color: white;
        padding: 0.8rem 2rem;
        border-radius: 6px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        margin-right: 1rem;
        transition: all 0.3s ease;
    }

    .btn-konsultasi:hover {
        background: #FF9800;
        color: white;
        transform: translateY(-2px);
    }

    /* Portfolio section */
    .portfolio-section {
        padding: 5rem 0;
        background: var(--white);
    }

    .portfolio-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-top: 3rem;
    }

    .portfolio-item {
        background: var(--light-gray);
        height: 200px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-text);
        font-size: 0.9rem;
        text-align: center;
        border: 1px solid var(--border-color);
    }

    /* Footer */
    .footer {
        background: var(--dark-text);
        color: white;
        padding: 3rem 0 1rem;
    }

    .footer-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-blue);
    }

    @media (max-width: 768px) {
        .hero-content h1 {
            font-size: 2.2rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .btn-portfolio, .btn-video {
            display: block;
            margin-bottom: 1rem;
            text-align: center;
        }
    }
</style>
</style>

</head>

<body>

<!-- Top Orange Bar -->
<div class="top-bar"></div>

<!-- Navigation -->
<nav class="navbar navbar-expand-lg fixed-top" style="top: 8px;">
    <div class="container">
        <a class="navbar-brand" href="#top">Desain<span style="color: var(--orange);">.In</span></a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto me-4">
                <li class="nav-item"><a class="nav-link" href="#produk">Produk</a></li>
                <li class="nav-item"><a class="nav-link" href="#pelanggan">Pelanggan</a></li>
                <li class="nav-item"><a class="nav-link" href="#konsultasi">Konsultasi</a></li>
                <li class="nav-item"><a class="nav-link" href="#portofolio">Portofolio</a></li>
            </ul>
            <a href="#mulai" class="btn-mulai">MULAI</a>
        </div>
    </div>
</nav>
<!-- Hero Section -->
<section class="hero-section" id="top" style="padding-top: 8rem;">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1>Bangun identitas visual kamu dengan desain modern, cepat, dan hemat budget.</h1>
                    <p>Dapatkan desain logo, brand identity, social media, dan marketing materials yang profesional dengan harga terjangkau. Proses cepat dan hasil memuaskan!</p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="#portfolio" class="btn-portfolio">🚀 Lihat Portfolio</a>
                        <a href="#video" class="btn-video">▶ Lihat Video</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image text-center">
                    <!-- Placeholder untuk gambar hero - Anda perlu menambahkan gambar di: assets/img/hero-illustration.png -->
                    <div style="width: 100%; height: 400px; background: var(--light-blue); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--primary-blue); font-size: 1.1rem; text-align: center;">
                        <div>
                            <div style="font-size: 3rem; margin-bottom: 1rem;">🎨</div>
                            <div>Hero Illustration<br><small>Tambahkan gambar di:<br>assets/img/hero-illustration.png</small></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- Features Section -->
<section class="features-section" id="produk">
    <div class="container">
        <h2 class="section-title">Saya bantu kamu tampil beda!</h2>
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <!-- Placeholder untuk icon - Anda perlu menambahkan icon di: assets/img/icons/creative-design.png -->
                        🎨
                    </div>
                    <h4>Creative Design</h4>
                    <p>Desain kreatif dan unik yang sesuai dengan brand identity kamu. Dari logo hingga marketing materials.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <!-- Placeholder untuk icon - Anda perlu menambahkan icon di: assets/img/icons/social-media.png -->
                        📱
                    </div>
                    <h4>Social Media Kit</h4>
                    <p>Template dan konten visual untuk semua platform social media. Instagram, Facebook, LinkedIn, dan lainnya.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <!-- Placeholder untuk icon - Anda perlu menambahkan icon di: assets/img/icons/brand-identity.png -->
                        🏷️
                    </div>
                    <h4>Brand Identity</h4>
                    <p>Paket lengkap brand identity termasuk logo, color palette, typography, dan brand guidelines.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon">
                        <!-- Placeholder untuk icon - Anda perlu menambahkan icon di: assets/img/icons/print-design.png -->
                        🖨️
                    </div>
                    <h4>Print Design</h4>
                    <p>Desain untuk keperluan cetak seperti brosur, flyer, business card, dan merchandise.</p>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Why Section -->
<section class="why-section" id="pelanggan">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="section-title text-start">Kenapa Harus Desain.In</h2>
                <p class="mb-4" style="color: var(--gray-text); font-size: 1.1rem;">
                    Kami memahami kebutuhan bisnis modern yang memerlukan identitas visual yang kuat namun tetap terjangkau. Dengan pengalaman bertahun-tahun, kami siap membantu mewujudkan visi brand kamu.
                </p>

                <div class="check-item">
                    <div class="check-icon">✓</div>
                    <div>
                        <h5 class="mb-2" style="color: var(--dark-text); font-weight: 600;">Proses Cepat & Efisien</h5>
                        <p style="color: var(--gray-text); margin-bottom: 0;">Dapatkan desain berkualitas dalam waktu 1-3 hari kerja. Tidak perlu menunggu berminggu-minggu.</p>
                    </div>
                </div>

                <div class="check-item">
                    <div class="check-icon">✓</div>
                    <div>
                        <h5 class="mb-2" style="color: var(--dark-text); font-weight: 600;">Harga Terjangkau</h5>
                        <p style="color: var(--gray-text); margin-bottom: 0;">Paket desain mulai dari 150rb. Kualitas premium dengan harga yang ramah di kantong.</p>
                    </div>
                </div>

                <div class="check-item">
                    <div class="check-icon">✓</div>
                    <div>
                        <h5 class="mb-2" style="color: var(--dark-text); font-weight: 600;">Revisi Unlimited</h5>
                        <p style="color: var(--gray-text); margin-bottom: 0;">Kami akan terus memperbaiki desain hingga kamu 100% puas dengan hasilnya.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <!-- Placeholder untuk gambar ilustrasi - Anda perlu menambahkan gambar di: assets/img/why-choose-us.png -->
                <div style="width: 100%; height: 350px; background: var(--light-blue); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--primary-blue); font-size: 1.1rem; text-align: center;">
                    <div>
                        <div style="font-size: 3rem; margin-bottom: 1rem;">💡</div>
                        <div>Why Choose Us Illustration<br><small>Tambahkan gambar di:<br>assets/img/why-choose-us.png</small></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Help Section -->
<section class="help-section" id="konsultasi">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h2 class="section-title">Bingung Mulai dari Mana? Saya Bantu! ⭐</h2>
                <p class="mb-4 fs-5" style="color: var(--gray-text);">
                    Lagi ngerjain proyek dan butuh bantuan? Mulai dari konsep, prototipe, sampai desain website dan UI/UX?
                </p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="#konsultasi" class="btn-konsultasi">Konsultasi Gratis</a>
                    <a href="#portfolio" class="btn-portfolio">Lihat Portfolio</a>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Portfolio Section -->
<section class="portfolio-section" id="portfolio">
    <div class="container">
        <div class="row align-items-center mb-5">
            <div class="col-lg-6">
                <h2 class="section-title text-start">Visual bukan sekedar cantik.</h2>
                <p class="mb-4" style="color: var(--gray-text); font-size: 1.1rem;">
                    Setiap desain yang kami buat memiliki tujuan dan strategi yang jelas. Bukan hanya indah dipandang, tapi juga efektif dalam menyampaikan pesan brand kamu kepada target audience.
                </p>
                <p class="mb-4" style="color: var(--gray-text); font-size: 1.1rem;">
                    Dari logo yang memorable, social media yang engaging, hingga marketing materials yang converting - semua dirancang dengan pendekatan yang tepat untuk mencapai goals bisnis kamu.
                </p>
            </div>
            <div class="col-lg-6">
                <!-- Placeholder untuk gambar ilustrasi - Anda perlu menambahkan gambar di: assets/img/portfolio-illustration.png -->
                <div style="width: 100%; height: 300px; background: var(--light-blue); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: var(--primary-blue); font-size: 1.1rem; text-align: center;">
                    <div>
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
                        <div>Portfolio Illustration<br><small>Tambahkan gambar di:<br>assets/img/portfolio-illustration.png</small></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="portfolio-grid">
            <div class="portfolio-item">
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎨</div>
                    <div>Portfolio Item 1<br><small>assets/img/portfolio/portfolio-1.jpg</small></div>
                </div>
            </div>
            <div class="portfolio-item">
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📱</div>
                    <div>Portfolio Item 2<br><small>assets/img/portfolio/portfolio-2.jpg</small></div>
                </div>
            </div>
            <div class="portfolio-item">
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🏷️</div>
                    <div>Portfolio Item 3<br><small>assets/img/portfolio/portfolio-3.jpg</small></div>
                </div>
            </div>
            <div class="portfolio-item">
                <div>
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🖨️</div>
                    <div>Portfolio Item 4<br><small>assets/img/portfolio/portfolio-4.jpg</small></div>
                </div>
            </div>
        </div>
    </div>
</section>




<!-- Footer -->
<footer class="footer" id="porotfolio">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 mb-4">
                <div class="footer-brand mb-3">Desain<span style="color: var(--orange);">.In</span></div>
                <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">
                    Solusi desain grafis terpercaya untuk kebutuhan bisnis modern. Wujudkan identitas visual yang kuat dengan harga terjangkau.
                </p>
            </div>
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-white mb-3" style="font-weight: 600;">Layanan</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Logo Design</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Brand Identity</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Social Media Kit</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Print Design</a></li>
                </ul>
            </div>
            <div class="col-lg-2 col-md-6 mb-4">
                <h6 class="text-white mb-3" style="font-weight: 600;">Perusahaan</h6>
                <ul class="list-unstyled">
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Tentang Kami</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Portfolio</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Testimoni</a></li>
                    <li class="mb-2"><a href="#" style="color: rgba(255,255,255,0.7); text-decoration: none; transition: color 0.3s;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.7)'">Kontak</a></li>
                </ul>
            </div>
            <div class="col-lg-4 mb-4">
                <h6 class="text-white mb-3" style="font-weight: 600;">Hubungi Kami</h6>
                <p style="color: rgba(255,255,255,0.8); margin-bottom: 0.5rem;">📧 <EMAIL></p>
                <p style="color: rgba(255,255,255,0.8); margin-bottom: 0.5rem;">📱 +62 812-3456-7890</p>
                <p style="color: rgba(255,255,255,0.8); margin-bottom: 1rem;">🕒 Senin - Jumat, 09:00 - 18:00 WIB</p>
            </div>
        </div>
        <hr style="border-color: rgba(255,255,255,0.2); margin: 2rem 0 1rem;">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p style="color: rgba(255,255,255,0.7); margin-bottom: 0; font-size: 0.9rem;">&copy; 2024 Desain.In. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <p style="color: rgba(255,255,255,0.7); margin-bottom: 0; font-size: 0.9rem;">Syarat & Ketentuan | Kebijakan Privasi</p>
            </div>
        </div>
    </div>
</footer>


<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JavaScript -->
<script>
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Navbar background on scroll
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 50) {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.backdropFilter = 'blur(10px)';
        } else {
            navbar.style.background = 'white';
            navbar.style.backdropFilter = 'none';
        }
    });
</script>

</body>
</html><?php /**PATH D:\xampp\htdocs\desainin_myproject\desainin\resources\views/landing.blade.php ENDPATH**/ ?>